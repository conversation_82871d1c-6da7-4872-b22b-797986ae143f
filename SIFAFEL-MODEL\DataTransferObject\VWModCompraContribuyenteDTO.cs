namespace SIFAFEL_MODEL.DataTransferObject
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class VWModCompraContribuyenteDTO
    {
        public int id_contribuyente { get; set; }
        public string nombre_contribuyente { get; set; }

        public int id_sucursal { get; set; }
        public string nombre_sucursal { get; set; }

        public int id_compra { get; set; }
        public int id_secuencia { get; set; }

        public int id_moneda { get; set; }
        public string moneda { get; set; }
        public double imp_neto { get; set; }
        public double imp_descuento { get; set; }
        public double imp_recargo { get; set; }
        public double imp_total { get; set; }
        public string imp_total_txt { get; set; }
        public DateTime fecha_registro { get; set; }
        public DateTime fecha_compra { get; set; }
        public string tipo { get; set; }

        public string cod_estado { get; set; }
        public string nom_estado { get; set; }
        public string class_color { get; set; }

        public int id_proveedor { get; set; }
        public string nit_proveedor { get; set; }
        public string nombre_proveedor { get; set; }

        public int id_usuario { get; set; }
        public string nombre_usuario { get; set; }

        public string usuario_img_perfil { get; set; }

        public string num_factura { get; set; }
        public string num_serie { get; set; }
        public string num_poliza { get; set; }
        public string aplica_duca { get; set; }
    }
}
